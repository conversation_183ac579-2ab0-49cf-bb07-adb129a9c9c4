<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测评任务页面加载测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-container {
            border: 1px solid #ddd;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
        }
        .test-button {
            background-color: #409eff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
        }
        .test-button:hover {
            background-color: #337ecc;
        }
        .test-result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background-color: #f0f9ff;
            border: 1px solid #67c23a;
            color: #67c23a;
        }
        .error {
            background-color: #fef0f0;
            border: 1px solid #f56c6c;
            color: #f56c6c;
        }
        .info {
            background-color: #f4f4f5;
            border: 1px solid #909399;
            color: #606266;
        }
        iframe {
            width: 100%;
            height: 600px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .test-log {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            max-height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>测评任务页面加载测试</h1>
    <p>此页面用于测试修复后的测评任务页面是否能够稳定加载右侧列表数据。</p>
    
    <div class="test-container">
        <h3>测试说明</h3>
        <p>1. 点击下方按钮多次刷新测评任务页面</p>
        <p>2. 观察右侧列表是否每次都能正确加载</p>
        <p>3. 检查浏览器控制台是否有错误信息</p>
        
        <button class="test-button" onclick="loadEvaluationTask()">加载测评任务页面</button>
        <button class="test-button" onclick="reloadPage()">重新加载页面</button>
        <button class="test-button" onclick="loadWithCatalog()">带catalog参数加载</button>
        <button class="test-button" onclick="clearLog()">清空日志</button>
        
        <div id="testResult" class="test-result info">
            等待测试...
        </div>
        
        <div class="test-log" id="testLog">
            测试日志将显示在这里...
        </div>
    </div>
    
    <div class="test-container">
        <h3>测评任务页面</h3>
        <iframe id="testFrame" src="about:blank"></iframe>
    </div>

    <script>
        let testCount = 0;
        
        function log(message) {
            const logElement = document.getElementById('testLog');
            const timestamp = new Date().toLocaleTimeString();
            logElement.innerHTML += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }
        
        function updateResult(message, type = 'info') {
            const resultElement = document.getElementById('testResult');
            resultElement.textContent = message;
            resultElement.className = `test-result ${type}`;
        }
        
        function loadEvaluationTask() {
            testCount++;
            log(`开始第 ${testCount} 次测试加载...`);
            updateResult(`正在进行第 ${testCount} 次测试...`, 'info');
            
            const iframe = document.getElementById('testFrame');
            iframe.src = 'http://localhost:8082/#/evaluation-task';
            
            // 监听iframe加载完成
            iframe.onload = function() {
                log(`第 ${testCount} 次加载完成`);
                updateResult(`第 ${testCount} 次测试完成，请检查右侧列表是否正确加载`, 'success');
                
                // 检查iframe中的内容
                setTimeout(() => {
                    try {
                        const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                        const tableElements = iframeDoc.querySelectorAll('.el-table__body tr');
                        if (tableElements.length > 0) {
                            log(`✓ 检测到 ${tableElements.length} 行表格数据`);
                        } else {
                            log(`⚠ 未检测到表格数据，可能存在加载问题`);
                        }
                    } catch (e) {
                        log(`无法访问iframe内容（跨域限制）: ${e.message}`);
                    }
                }, 2000);
            };
            
            iframe.onerror = function() {
                log(`第 ${testCount} 次加载失败`);
                updateResult(`第 ${testCount} 次测试失败`, 'error');
            };
        }
        
        function reloadPage() {
            log('重新加载当前页面...');
            const iframe = document.getElementById('testFrame');
            iframe.src = iframe.src;
        }
        
        function loadWithCatalog() {
            testCount++;
            log(`开始第 ${testCount} 次测试加载（带catalog参数）...`);
            updateResult(`正在进行第 ${testCount} 次测试（带参数）...`, 'info');
            
            const iframe = document.getElementById('testFrame');
            iframe.src = 'http://localhost:8082/#/evaluation-task?catalog=ROOT';
            
            iframe.onload = function() {
                log(`第 ${testCount} 次加载完成（带参数）`);
                updateResult(`第 ${testCount} 次测试完成（带参数），请检查右侧列表是否正确加载`, 'success');
            };
        }
        
        function clearLog() {
            document.getElementById('testLog').innerHTML = '测试日志已清空...\n';
            testCount = 0;
            updateResult('日志已清空，等待新的测试...', 'info');
        }
        
        // 页面加载完成后的初始化
        window.onload = function() {
            log('测试页面初始化完成');
            updateResult('测试环境准备就绪，可以开始测试', 'success');
        };
    </script>
</body>
</html>
