<template>
  <page-wrapper :route-name="`${routeName}::`">
    <div class="main-wrapper">
      <el-card class="left-card">
        <el-tree
          style="max-width: 600px"
          node-key="id"
          :current-node-key="activeId"
          :data="productList"
          :props="defaultProps"
          :highlight-current="true"
          :default-expanded-keys="['all']"
          @node-click="handleNodeClick"
        />
      </el-card>
      <div class="compVersion-list height-adaptive">
        <el-card class="info-card">
          <el-collapse
            v-model="activeCollapse"
            @change="events.collapseChange"
            class="collapse"
          >
            <el-collapse-item :name="1">
              <template #title><span>基本信息</span></template>
              <el-descriptions :column="2">
                <el-descriptions-item
                  label-class-name="bold"
                  label="产品名称："
                  >{{ getText(productDetail.name) }}</el-descriptions-item
                >
                <el-descriptions-item
                  label-class-name="bold"
                  label="产品编码："
                  >{{ getText(productDetail.code) }}</el-descriptions-item
                >
                <el-descriptions-item
                  label-class-name="bold"
                  label="产品描述："
                  >{{
                    getText(productDetail.description)
                  }}</el-descriptions-item
                >
                <el-descriptions-item
                  label-class-name="bold"
                  label="验证环境："
                  >{{
                    getText(
                      regionList[productDetail.region] ?? productDetail.region
                    )
                  }}</el-descriptions-item
                >
              </el-descriptions>
            </el-collapse-item>
          </el-collapse>
        </el-card>

        <el-card class="table-card" style="flex: 1">
          <div style="display: flex">
            <el-descriptions>
              <template #title>
                <span>版本信息</span>&nbsp;
                <el-button link type="primary" @click="events.searchQuery">
                  <el-icon size="18"><Refresh /></el-icon>
                </el-button>
              </template>
            </el-descriptions>
            <my-button
              type="primary"
              @click="events.add"
              :icon="CirclePlus"
              style="margin-left: auto"
              :operationAuth="authStr"
              >创建分流版本</my-button
            >
          </div>
          <div style="height: calc(100% - 40px)">
            <table-page
              name="trafficTable"
              ref="myTableRef"
              :query="query"
              :columns="columns"
              :operations="operations"
              :operationAuth="authStr"
              :loadDataApi="getTrafficList"
              :transformQuery="transformQuery"
              :transformListData="transformListData"
              @operation="handleOperation"
              :loadImmediately="false"
            >
              <template #query>
                <div class="flexBetween">
                  <my-query
                    :refresh-btn="{ show: true }"
                    :queryItems="queryItems"
                    @search="events.searchQuery"
                    @reset="events.reset"
                  />
                </div>
              </template>
            </table-page>
          </div>
        </el-card>
      </div>
    </div>
  </page-wrapper>
  <AddDialog
    ref="addRef"
    @reload="loadList"
    :productList="productList"
    :activeId="activeId"
    @canvas="events.toFlow"
  />
  <RecordDialog
    ref="productRef"
    :title="drawerConfig.title"
    :columns="drawerConfig.columns"
    :query="drawerConfig.query"
    :loadDataApi="drawerConfig.api"
  />
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, computed, watch } from "vue";
import useStore from "@/store";
import { assign, cloneDeep } from "lodash";
import { timeC } from "turing-plugin";
import { useI18n } from "vue-i18n";
import { getText } from "@/utils/helpers";
import useCtx from "@/hooks/useCtx";
import { CirclePlus } from "@element-plus/icons-vue";
import { getRegionListApi } from "@/views/common/common.ts";
import AddDialog from "./add.vue";
import RecordDialog from "@/views/business-manage/app/recordList.vue";
import {
  getProductList,
  getProductDetail,
  getOnlineEnabledProductList,
  getOpenedProductList,
} from "@/api/product.ts";
import {
  getTrafficList,
  deleteTraffic,
  enableOrDisableTraffic,
  publishTraffic,
  copyTraffic,
  handleDocument,
} from "@/api/traffic.ts";
const { common } = useStore();

const { t } = useI18n();
const { $app, proxy, $router, $auth } = useCtx();
const routeName = "traffic";
const activeCollapse = ref([1]);
// 产品列表
const productList = ref([]);
const activeId = ref(
  localStorage.getItem(`${$app.$route.path}TreeId`) || "all"
);
let activeNodeInfo = ref({});

const defaultProps = {
  children: "children",
  label: "label",
};
const authStr = computed(() => {
  //2代表线上环境 1代表验证环境
  return $router.currentRoute.value.name == "traffic"
    ? "/base/#/traffic/edit"
    : "/base/#/traffic-verify/edit";
});
const queryItems = ref<any>({
  status: {
    label: "状态",
    type: "select",
    options: [
      { value: 1, label: "草稿" },
      { value: 2, label: "已发布" },
      { value: 3, label: "已归档" },
    ],
  },
});
const envType = computed(() => {
  //2代表线上环境 1代表验证环境
  return $router.currentRoute.value.name == "traffic" ? 2 : 1;
});
const getTreeList = () => {
  const func =
    envType.value == 2 ? getOnlineEnabledProductList : getOpenedProductList;
  func().then((res: any) => {
    const products = res.data.map((item: any, index: number) => ({
      label: `${index + 1}. ${item.name}`,
      id: item.id,
      code: item.code,
      value: item.id,
    }));
    productList.value = [
      {
        id: "all",
        label: "全部",
        children: products,
      },
    ];
    activeNodeInfo.value = products.find((item) => item.id == activeId.value);
    getDetail(activeId.value);
    loadList();
  });
};
getTreeList();
// 点击树节点获取产品详情及分流列表
const currentProductId = ref("");
const currentProductName = ref("");
const handleNodeClick = (data: any) => {
  activeId.value = data.id;
  if (data.id != "all") {
    currentProductId.value = data.id;
    currentProductName.value = data.label;
    getDetail(data.id);
    loadList();
  } else {
    currentProductId.value = "";
    currentProductName.value = "";
    productDetail.value = {};
    loadList();
  }
  localStorage.setItem(`${$app.$route.path}TreeId`, data.id);
};

// 产品详情
const productDetail = ref<any>({});
const getDetail = (id: string) => {
  getProductDetail(id).then((res: any) => {
    productDetail.value = res || {};
  });
};

// 产品列表 && 操作记录
const drawerConfig = reactive({
  title: "操作记录",
  query: {},
  api: getProductList,
  columns: [
    { prop: "createdDate", label: "操作时间", width: 180 },
    { prop: "type", label: "类型" },
    {
      prop: "description",
      label: "描述",
      showOverflowTooltip: false,
      width: 150,
    },
    { prop: "createdBy", label: "操作人", width: 150 },
  ],
});

/* 查询 */
const query = ref<any>({});

/* 表格 */
const columns = ref([
  {
    prop: "enabled",
    label: "是否启用",
    width: 110,
    custom: "switch",
    customRender: {
      attrs: {
        "active-value": true,
        "inactive-value": false,
      },
      beforeChange: (record: any) => {
        if (record.status == 3) {
          $app.$message.warning("当前版本已归档，无法再启动");
          return;
        }
        $app
          .$confirm({
            title: `确定${record.enabled ? "停用" : "启用"}分流版本“${
              record.name
            }”吗？`,
          })
          .then(() => {
            enableOrDisableTraffic({
              id: record.id,
              enabled: !record.enabled,
              envType: envType.value,
            }).then(() => {
              loadList();
            });
          });
      },
    },
  },
  // 设置宽度的时候，需要至少保留一个width不是固定的，可用minWidth代替，否则可能出现大屏表格宽度未到100%的情况
  // 可点击项
  {
    prop: "nameRender",
    label: "分流版本名称",
    width: 200,
    blod: true,
    custom: "link",
    customRender: {
      disabled: (record: any) => record.status ==3,
      click: (record: any) => {
        events.toFlow(record);
      },
    },
  },
  { prop: "description", label: "分流版本描述", minWidth: 200 },
  {
    prop: "status",
    label: "状态",
    width: 90,
    custom: "status",
    // 根据状态实际取值来定义
    customRender: {
      options: {
        1: { type: "warning", name: "草稿" },
        2: { type: "success", name: "已发布" },
        3: { type: "danger", name: "已归档" },
      },
    },
  },

  { prop: "lastModifiedBy", label: "更新人", width: 100 },
  { prop: "lastModifiedDate", label: "更新时间", width: 160 },
  { prop: "createdBy", label: "创建人", width: 100 },
  { prop: "createdDate", label: "创建时间", width: 160 },
  { prop: "operation", label: "操作", width: 120, fixed: "right" },
]);
const operations = [
  {
    type: "edit",
    label: t("btn.edit"),
    disabled: (record: any) => record.status == 3,
    disabledTips: (record) => {
      if (record.status == 3) {
        return "版本已经归档，不可编辑";
      }
    },
  },
  {
    type: "publish",
    label: "发布版本",
    disabled: (record: any) => record.status != 1,
    collapsed: true,
    disabledTips: (record) => {
      if (record.status == 2) {
        return "版本已经发布，不可发布版本";
      }
      if (record.status == 3) {
        return "版本已经归档，不可发布版本";
      }
    },
  },
  { type: "copy", label: "复制版本", collapsed: true },
  {
    type: "delete",
    label: `${t("btn.delete")}版本`,
    btnType: "danger",
    disabled: (record: any) => record.enabled,
    disabledTips: "存在已关联产品的版本禁止删除",
    collapsed: true,
  },
  { type: "record", label: "操作记录", collapsed: true },
  {
    type: "documentation",
    label: "归档",
    collapsed: true,
    disabled: (record: any) => record.enabled||record.status == 3,
    disabledTips:  (record) => {
      if (record.enabled) {
        return "只有禁用后才能操作归档";
      }
      if (record.status == 3) {
        return "当前版本已经归档，不可再归档";
      }
    },
  },
];
const handleOperation = (data: any) => {
  const { type, record } = data;
  typeof events[type] == "function" && events[type](record);
};

// 转换传参
const transformQuery = ({ ...rest }) => {
  const query = {
    envType: envType.value,
    productId: activeId.value == "all" ? "" : activeId.value,
    ...rest,
  };
  return query;
};

// 转换接口返回的数据
const transformListData = (data: any) => {
  return data.map((x: any) => {
    x.nameRender = `${x.name}`;
    x.createdDate = timeC.format(x.createdDate, "YYYY-MM-DD hh:mm:ss");
    x.lastModifiedDate = timeC.format(
      x.lastModifiedDate,
      "YYYY-MM-DD hh:mm:ss"
    );
    return x;
  });
};

/* events */
const events = reactive({
  collapseChange: (val: string[]) => {
    setTimeout(() => {
      proxy.$refs.myTableRef.mediaHeight();
    }, 500);
  },
  searchQuery: (obj: any) => {
    query.value = assign({}, query.value, obj);
  },
  delete: (record: any) => {
    $app
      .$deleteConfirm({
        title: t("tip.deleteConfirmTitle", { t: "分流版本", n: record.name }),
      })
      .then(() => {
        deleteTraffic(record.id).then((res: any) => {
          $app.$message.success("分流版本删除成功！");
          loadList();
        });
      });
  },
  documentation: (record: any) => {
    $app
        .$confirm({ title: `归档后不能再次启用，确认对该条记录进行归档吗？` })
        .then(() => {
          handleDocument(record.id).then((res: any) => {
            $app.$message.success("归档成功");
            loadList();
          });
        });
  },
  add: () => {
    proxy.$refs.addRef?.openDialog("add", {
      productId: currentProductId.value,
      name: currentProductName.value,
    });
  },
  edit: (record: any) => {
    proxy.$refs.addRef?.openDialog("edit", record);
  },
  copy: (record: any) => {
    $app
      .$confirm({ title: `您确认要复制分流版本“${record.name}”吗？` })
      .then(() => {
        copyTraffic(record.id).then((res: any) => {
          $app.$message.success("分流版本复制成功！");
          loadList();
        });
      });
  },
  publish: (record: any) => {
    $app
      .$confirm({ title: `您确认要发布分流版本“${record.name}”吗？` })
      .then(() => {
        publishTraffic(record.id).then((res: any) => {
          $app.$message.success("分流版本发布成功！");
          loadList();
        });
      });
  },
  record: (record: any) => {
    proxy.$refs.productRef?.openDialog(record.id);
  },
  // 流程拓扑
  toFlow: (record: any) => {
    //草稿状态且具有编辑权限才能编辑画布
    if (
      (record.status == 1 || !record.status) &&
      $auth.testAuth(authStr.value)
    ) {
      window.history.pushState(
        {},
        "",
        `${
          (window as any).SYSTEM_CONFIG_BASEURL
        }/astrolink-ui/#/flow/detail?id=${record.productId}&vid=${
          record.id
        }&t=${envType.value == 2 ? "traffic" : "trafficVerify"}`
      );
    } else {
      window.history.pushState(
        {},
        "",
        `${(window as any).SYSTEM_CONFIG_BASEURL}/astrolink-ui/#/flow/view?id=${
          record.productId
        }&vid=${record.id}&t=${
          envType.value == 2 ? "traffic" : "trafficVerify"
        }`
      );
    }
  },
});

/* 列表刷新 */
const loadList = () => {
  proxy.$refs.myTableRef.loadData();
};
const regionList = ref([]);
onMounted(async () => {
  regionList.value = await getRegionListApi(1);
});
</script>

<style lang="scss" scoped>
.main-wrapper {
  display: flex;
  padding: 20px;
  overflow: hidden;

  .left-card {
    width: 220px;
    height: 100%;
    overflow-y: auto;
    overflow-x: hidden;

    :deep(.el-card__body) {
      padding: 10px;
    }
  }
}
.compVersion-list {
  padding-left: 10px;
  width: calc(100% - 220px);

  .el-card {
    :deep(.el-card__body) {
      height: 100%;
    }
  }

  .info-card {
    height: auto;
    margin-bottom: 10px;

    :deep(.el-descriptions__label.bold) {
      font-weight: bold;
      background: #fff !important;
    }
  }

  :deep(.query-wrapper),
  :deep(.table-wrapper) {
    padding: 0;
  }
}
</style>
