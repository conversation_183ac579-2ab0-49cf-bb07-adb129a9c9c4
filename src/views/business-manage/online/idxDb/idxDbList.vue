<template>
  <div class="idx-db-list">
    <AddDialog ref="addRef" @reload="loadList" />
    <description-edit ref="descriptionEditRef" @save-data="events.modifyDescription"></description-edit>
    <table-page
      ref="myTableRef"
      name="idxDB"
      :query="query"
      :columns="columns"
      :operations="operations"
      :loadDataApi="loadListData"
      operationAuth="/base/#/online/edit"
      :transformListData="transformListData"
      @operation="handleOperation"
    >
      <!-- 查询 + 操作插槽内容 -->
      <template #query>
        <div class="flexBetween">
          <my-query :queryItems="queryItems" :refreshBtn="{ show: true }" @search="events.search" @reset="events.reset" />
          <my-operation style="margin-bottom: 12px; display: flex; justify-content: flex-end">
            <template #buttonGroup>
              <my-button type="export" @click="events.export" operationAuth="/base/#/online/edit">导出</my-button>
              <my-button type="add" @click="events.add" operationAuth="/base/#/online/edit">新建上线计划</my-button>
            </template>
          </my-operation>
        </div>
      </template>
      <template #process="scope">
        <status-dot v-if="dataC.isEmpty(scope.row.taskDetailList)" type="info" name="待同步" />
        <task-process
          v-if="!dataC.isEmpty(scope.row.taskDetailList)"
          :taskId="scope.row.taskId"
          :datasetVersionId="scope.row.datasetVersionId"
          :id="scope.row.id"
          :taskType="scope.row.taskType"
          :taskStatus="scope.row.taskStatus"
          :taskList="scope.row.taskDetailList"
          @cancel-task="loadList"
          :disabled="!testAuth()"
        ></task-process>
      </template>
    </table-page>
    <my-drawer direction="ltr" class="mock-add" width="1500px" v-model="historyVisible" title="历史任务" :showConfirm="false" @close="historyVisible = false">
      <HistoryTask ref="historyTaskRef" :dataType="3" :queryDisplay="false"> </HistoryTask>
    </my-drawer>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, onMounted, onUnmounted, nextTick } from "vue";
import { assign } from "lodash";
import { dataC, timeC } from "turing-plugin";
import descriptionEdit from "@/views/common/DescriptionEdit.vue";
import useCtx from "@/hooks/useCtx";
import AddDialog from "./idxDbAdd.vue";
import taskProcess from "@/views/common/task/TaskProcess.vue";
import HistoryTask from "@/views/common/task/TaskHistory.vue";
import {
  getIdxDbOnlinePlanList,
  syncIdxDbOnlinePlan,
  replayIdxDbOnlinePlan,
  deleteIdxDbOnlinePlan,
  clearIdxDb,
  clearIdxDbData,
  enabledIdxDb,
  disabledIdxDb,
  getMetaRegionList,
  editIdxDbOnlinePlanBase,
  exportIdxDbInstList,
} from "@/api/online.ts";
import * as taskApi from "@/api/task";
import IntervalClient from "@/utils/interval-client";
import * as util from "@/utils/common";

const { $app, proxy, $router, $auth } = useCtx();
const historyVisible = ref(false);
const testAuth = () => {
  return $auth.testAuth("/base/#/online/edit");
};
const metaRegionList = ref([]);
/* 查询 */
const query = ref<any>({});
const queryItems = ref<any>({
  search: {
    type: "input",
    width: "240px",
    modelValue: "",
    attrs: {
      placeholder: "名称",
    },
  },
  enabled: {
    type: "select",
    label: "",
    modelValue: undefined,
    options: [
      { value: true, label: "已启用" },
      { value: false, label: "已禁用" },
    ],
    attrs: {
      placeholder: "启用状态",
    },
  },
  realtime: {
    type: "select",
    label: "",
    modelValue: undefined,
    options: [
      { value: true, label: "实时" },
      { value: false, label: "非实时" },
    ],
    attrs: {
      placeholder: "支持实时",
    },
  },
  targetRegion: {
    type: "select",
    label: "",
    modelValue: undefined,
    options: computed(() => {
      return metaRegionList.value.filter((item) => item.envType == 2).map((item) => ({ label: item.name, value: item.code }));
    }),
    attrs: {
      placeholder: "上线环境",
    },
  },
  status: {
    type: "select",
    label: "",
    modelValue: undefined,
    options: [
      { value: 1, label: "未归档" },
      { value: 3, label: "已归档" },
    ],
    attrs: {
      placeholder: "归档状态",
    },
  },
});
//列表查询
const loadListData = async (data: any) => {
  if (dataC.isEmpty(metaRegionList.value)) {
    const result = await getMetaRegionList();
    metaRegionList.value.push(...result.content);
  }
  return new Promise((resolve: any) => {
    getIdxDbOnlinePlanList(data).then((result) => {
      //增加任务监控
      getIntervalClinet(result.content);
      //返回数据
      resolve(result);
    });
  });
};
//定时任务监控
const intervalClinet = ref(null);
const getIntervalClinet = (tableData: Array<any>) => {
  //如果已有定时任务对象，则停止并创建新的
  intervalClinet.value?.disconnect();
  //如果列表为空 则不创建新的
  if (dataC.isEmpty(tableData)) return;
  // 获取定时任务对象并启动,以持续刷新任务信息
  intervalClinet.value = new IntervalClient(3000, true);
  intervalClinet.value.onHandler(getTaskProgressListByTask, tableData).connect();
};
//获取任务信息
const getTaskProgressListByTask = (tableData: Array<any>) => {
  const taskIdList = tableData
    .filter((x) => {
      return !dataC.isEmpty(x.taskId);
    })
    .map((x: any) => {
      return x.taskId;
    });
  if (dataC.isEmpty(taskIdList)) return;
  taskApi.getTaskProgressListByTask(taskIdList).then((result) => {
    if (!(result.content instanceof Array)) return;
    const list = tableData.map((x: any) => {
      const task = dataC.getItemByValue(result.content, x.taskId, "taskId");
      if (!dataC.isEmpty(task.taskId)) {
        x.taskStatus = task.status;
        x.taskType = task.type;
        x.taskDetailList = task.taskDetailList;
      }
      return x;
    });
    proxy.$refs["myTableRef"].setTableData(list);
  });
};
/* 表格 */
const columns = ref([
  // 设置宽度的时候，需要至少保留一个width不是固定的，可用minWidth代替，否则可能出现大屏表格宽度未到100%的情况
  // 可点击项
  {
    prop: "name",
    label: "名称",
    width: 180,
    custom: "editLink",
    blod: true,
    customRender: {
      linkClick: (record: any) => {
        events.statistic(record);
      },
      linkDisabled: (record: any) => {
        return dataC.isEmpty(record.idInRegion);
      },
      btnClick: (record: any) => {
        events.openDescriptionEditWindow(record);
      },
      btnDisabled: (record: any) => {
        return !testAuth();
      },
    },
  },
  { prop: "idxDbInstNameRender", label: "索引库实例", width: 200, sortable: false },
  { prop: "idxDbInstCode", label: "编码", width: 260, withCopy: true, sortable: false },
  { prop: "statusRender", label: "状态", width: 100, sortable: false,
  custom: "status",
    // 根据状态实际取值来定义
    customRender: {
      options: {
        3: { type: "warning", name: "已归档" },
      },
    },
},
  {
    prop: "enabled",
    label: "启用状态",
    width: 110,
    custom: "switch",
    customRender: {
      attrs: {
        activeValue: true,
        inactiveValue: false,
        size: "small",
      },
      disabled: (record: any) => !record.enabled && record.taskStatus != 3,
      beforeChange: (record: any) => {
        return new Promise((resolve: any, reject: any) => {
          if (record.enabled === true) {
            $app
              .$confirm({ title: `确定禁用 ${record.name}?` })
              .then(() => {
                disabledIdxDb(record.id).then((result) => {
                  loadList();
                  resolve(true);
                  $app.$message.success(`禁用 ${record.name} 成功`);
                });
              })
              .catch(() => {
                reject();
              });
          } else {
            $app
              .$confirm({ title: `确定启用 ${record.name}?` })
              .then(() => {
                enabledIdxDb(record.id).then((result) => {
                  loadList();
                  resolve(true);
                  $app.$message.success(`启用 ${record.name} 成功`);
                });
              })
              .catch(() => {
                reject();
              });
          }
        });
      },
    },
  },
  { prop: "process", label: "任务进度", slotName: "process", width: 300, showOverflowTooltip: false, sortable: false },
  { prop: "indexDbCount", label: "数据总量", width: 120, sortable: false },
  { prop: "realtimeRender", label: "支持实时", width: 120, sortable: false },
  { prop: "targetRegionRender", label: "上线环境", width: 130 },
  {
    prop: "description",
    label: "描述",
    minWidth: 200,
    custom: "editButton",
    customRender: {
      btnClick: (record: any) => {
        events.openDescriptionEditWindow(record);
      },
      btnDisabled: (record: any) => {
        return !testAuth();
      },
    },
  },
  { prop: "lastModifiedBy", label: "更新人", width: 100 },
  { prop: "lastModifiedDateRender", label: "更新时间", width: 170 },
  { prop: "createdBy", label: "创建人", width: 100 },
  { prop: "createdDateRender", label: "创建时间", width: 170 },
  { prop: "operation", label: "操作", width: 220, fixed: "right" },
]);
const operations = [
  {
    type: "edit",
    label: "编辑",
    disabled: (record: any) => record.taskStatus != 1 || record.status == 3,
    disabledTips: (record: any) => {
      if (record.taskStatus != 1) {
        return "已同步，不可编辑";
      }
      if (record.status == 3) {
        return "已归档，不可编辑";
      }
      return "不可编辑";
    },
  },
  {
    type: "sync",
    label: "同步",
    disabled: (record: any) => record.taskStatus == 2 || record.status == 3,
    disabledTips: (record: any) => {
      if (record.taskStatus == 2) {
        return "任务正在执行中，不可同步";
      }
      if (record.status == 3) {
        return "已归档，不可同步";
      }
      return "不可同步";
    },
  },
  {
    type: "replay",
    label: "回放",
    disabled: (record: any) => record.taskStatus == 2 || (record.taskType == 8 && record.taskStatus != 3) || record.status == 3,
    disabledTips: (record: any) => {
      if (record.taskStatus == 2) {
        return "任务正在执行中，不可回放";
      }
      if (record.taskType == 8 && record.taskStatus != 3) {
        return "上次同步任务未成功，不可回放";
      }
      if (dataC.isEmpty(record.taskType)) {
        return "未执行同步任务，不可回放";
      }
      if (record.status == 3) {
        return "已归档，不可回放";
      }
      return "不可回放";
    },
  },
  {
    type: "delete",
    label: "归档上线计划",
    btnType: "danger",
    collapsed: true,
    disabled: (record: any) => record.enabled == true || record.taskStatus == 2 || record.status == 3,
    disabledTips: (record: any) => {
      if (record.enabled == true) {
        return "已启用，不可归档";
      }
      if (record.taskStatus == 2) {
        return "任务正在执行中，不可归档";
      }
      if (record.status == 3) {
        return "已归档，不可再归档";
      }
      return "不可归档";
    },
  },
  {
    type: "idxDel",
    label: "删除索引库",
    btnType: "danger",
    collapsed: true,
    disabled: (record: any) => record.enabled == true || record.taskStatus == 2 || record.status == 3,
    disabledTips: (record: any) => {
      if (record.enabled == true) {
        return "已启用，不可删除";
      }
      if (record.taskStatus == 2) {
        return "任务正在执行中，不可删除";
      }
      if (record.status == 3) {
        return "已归档，不可删除";
      }
      return "不可删除";
    },
  },
  {
    type: "history",
    label: "历史任务",
    btnType: "primary",
    auth: "all",
    collapsed: true,
  },
];
const handleOperation = (data: any) => {
  const { type, record } = data;
  if (type === "edit") {
    proxy.$refs.addRef?.openDialog("edit", record);
  } else {
    typeof events[type](record) == "function" && events[type](record);
  }
};
// 转换接口返回的数据
const transformListData = (data: any) => {
  return data.map((x: any) => {
    x.idxDbInstNameRender = `${x.idxDbInstName}(${x.idxDbInstEnName})`;
    x.indexDbCount = util.formatNumber(x.indexDbCount);
    x.realtimeRender = x.realtime ? "实时" : "非实时";
    x.targetRegionRender = dataC.getItemByValue(metaRegionList.value, x.targetRegion, "code")["name"];
    x.lastModifiedDateRender = !dataC.isEmpty(x.lastModifiedDate) ? timeC.format(x.lastModifiedDate, "YYYY-MM-DD hh:mm:ss") : "";
    x.createdDateRender = !dataC.isEmpty(x.createdDate) ? timeC.format(x.createdDate, "YYYY-MM-DD hh:mm:ss") : "";
    x.statusRender = x.status == 3 ? 3 : '';
    return x;
  });
};

/* events */
const events = reactive({
  history: (record: any) => {
    historyVisible.value = true;
    if (dataC.isEmpty(proxy.$refs.historyTaskRef)) {
      nextTick(() => {
        proxy.$refs.historyTaskRef.loadList(record.id);
      });
    } else {
      proxy.$refs.historyTaskRef.loadList(record.id);
    }
  },
  search: (obj: any) => {
    query.value = assign({}, query.value, obj);
  },
  reset: (obj: any) => {},
  statistic: (record: any) => {
    $router.push({
      name: `online::statistic`,
      query: {
        idxDbInstId: record.idxDbInstId,
        idxDbOnlinePlanId: record.id,
        metaLabel: ["统计信息"],
      },
    });
  },
  add: () => {
    proxy.$refs.addRef?.openDialog("add");
  },
  sync: (record: any) => {
    $app.$confirm({ title: `您确认要同步“${record.name}”数据吗？` }).then((res) => {
      syncIdxDbOnlinePlan(record.id).then((res) => {
        $app.$message.success("已添加同步任务！");
        loadList();
      });
    });
  },
  replay: (record: any) => {
    $app.$confirm({ title: `您确认要回放“${record.name}”数据吗？` }).then((res) => {
      replayIdxDbOnlinePlan(record.id).then((res) => {
        $app.$message.success("已添加回放任务");
        loadList();
      });
    });
  },
  delete: (record: any) => {
    $app
      .$confirm({
        title: `归档后索引库将无法使用，是否确认将“${record.name}”归档吗？`,
      })
      .then(() => {
        deleteIdxDbOnlinePlan(record.id).then(() => {
          loadList();
        });
      });
  },
  idxDel: (record: any) => {
    $app.$confirm({ title: `您确认要清除“${record.name}”的已上线数据吗？` }).then((res) => {
      clearIdxDb(record.id).then((res) => {
        $app.$message.success("清除数据成功!");
        loadList();
      });
    });
  },
  idxClear: (record: any) => {
    $app.$confirm({ title: `您确认要清空“${record.name}”数据吗？` }).then((res) => {
      clearIdxDbData(record.id).then((res) => {
        $app.$message.success("清除数据成功!");
        loadList();
      });
    });
  },
  openDescriptionEditWindow(record: any) {
    proxy.$refs["descriptionEditRef"].openWindow(record);
  },
  modifyDescription(record: any) {
    editIdxDbOnlinePlanBase(record).then((result) => {
      $app.$message.success("修改成功");
      proxy.$refs["descriptionEditRef"].closeWindow();
      loadList();
    });
  },
  export: () => {
    exportIdxDbInstList().then((result) => {
      util.downloadFile(result, `索引库.xlsx`);
    });
  },
});
/* 列表刷新 */
const loadList = () => {
  proxy.$refs.myTableRef.loadData();
};
//初始化
onMounted(() => {});
//销毁
onUnmounted(() => {
  intervalClinet.value?.disconnect();
});
//接口暴露
defineExpose({
  loadList,
});
</script>

<style lang="scss">
.idx-db-list {
  height: 100%;
}
</style>