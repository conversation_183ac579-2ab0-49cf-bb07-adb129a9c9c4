<template>
  <page-wrapper route-name="evaluation-task::details::">
    <div class="evaluation-task-container">
      <el-card class="info-card" :style="{ height: activeCollapse == 1 ? '170px' : '60px' }">
        <el-collapse v-model="activeCollapse" @change="events.collapseChange" class="collapse">
          <el-collapse-item :name="1">
            <template #title>
              <span style="font-size: 16px; font-weight: 700">基本信息</span>
            </template>
            <el-descriptions column="4">
              <el-descriptions-item label="任务名称 : " label-class-name="bold">{{ `${routeQuery.name}`
                }}</el-descriptions-item>

              <el-descriptions-item label="分组 : " label-class-name="bold">{{ routeQuery.label }}</el-descriptions-item>
              <el-descriptions-item label="query条数: " label-class-name="bold">{{ routeQuery.queryCount
                }}</el-descriptions-item>
              <el-descriptions-item label="参与人数 : " label-class-name="bold">{{ routeQuery.userCount
                }}</el-descriptions-item>
              <el-descriptions-item label="数据分配方式 : " label-class-name="bold">{{ routeQuery.typeRender || "无"
                }}</el-descriptions-item>
              <el-descriptions-item :span="1" label="测评配置选择 : " label-class-name="bold">{{
                routeQuery.markCategoryIdRender || "无"
                }}</el-descriptions-item>
              <el-descriptions-item :span="3" label="活动时间安排 : " label-class-name="bold">{{ routeQuery.timeRender || "无"
                }}</el-descriptions-item>
              <el-descriptions-item label="任务描述 : " label-class-name="bold" :span="4">{{
                routeQuery.description
                }}</el-descriptions-item>
            </el-descriptions>
          </el-collapse-item>
        </el-collapse>
      </el-card>
      <el-card class="table-card" :style="{
        height:
          activeCollapse == 1 ? 'calc(100vh - 320px)' : 'calc(100vh - 210px)',
      }">
        <div class="el-descriptions">
          <div class="el-descriptions__header">
            <div class="el-descriptions__title">
              <span>{{ "任务详情" }}</span>
              &nbsp;
              <el-button link type="primary" @click="loadList">
                <el-icon size="18">
                  <Refresh />
                </el-icon>
              </el-button>
              <my-select v-model="topK" :options="topKOptions" :style="{ width: '180px' }" placeholder="topK"
                @change="events.handleTopKChange" :clearable=false></my-select>

              <my-select v-model="userGroup" :options="userGroupOptions" :style="{ width: '180px', marginLeft: '20px' }"
                placeholder="分组" @change="events.handleUserGroupChange" :clearable=true></my-select>

            </div>
            <my-button type="export" @click="events.exportExcel" style="margin-left: 20px">导出</my-button>
          </div>
        </div>

        <div class="idx-db-statistic-site-table">
          <table-page ref="myTableRef" :columns="columns" :loadDataApi="loadListData" :withPagination="false"
            :transformListData="transformListData" :withSort="false">
          </table-page>
        </div>
      </el-card>
    </div>
    <!-- {{ routeQuery }} -->
  </page-wrapper>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, nextTick, computed } from "vue";
import { dataC, timeC } from "turing-plugin";
import useCtx from "@/hooks/useCtx";
import useStore from "@/store";
import * as evaluationApi from "@/api/eval-task";
import * as util from "@/utils/common";
const { $router, proxy, $app } = useCtx();
const { api } = useStore();
import * as missionApi from "@/api/eval-manage";
import * as commonApi from "@/api/common";
const mode = $router.currentRoute.value.query.mode;
let metaLabel = $router.currentRoute.value.query.metaLabel;
if (!(metaLabel instanceof Array)) {
  metaLabel = [metaLabel];
}
const routeQuery = $app.$route.query;
//事件声明
const emit = defineEmits(["preview-data"]);
const activeCollapse = ref([1]);

const columns = ref<any[]>([]);
//列配置
const defaultColumns = ref([
  {
    prop: "markUser",
    label: "标注人",
    width: 100,
  },
  {
    prop: "userGroup",
    label: "分组",
    width: 100,
  },
  {
    prop: "assignQueryCount",
    label: "query条数",
    width: 150,
  },

  {
    prop: "completeQueryCount",
    label: "已完成query条数",
    width: 160,
    custom: "link",
    blod: true,
    customRender: {
      click: (item: any) => {
        handleRirect(item, 'finishQuery', '已完成query条数')
      },
    },
  },
  {
    prop: "noCompleteQueryCount",
    label: "未完成query条数",
    width: 150,
  },
  {
    prop: "completePercentageRender",
    label: "完成率",
    width: 100,
  },
  {
    prop: "sceneProcessName",
    label: "策略名称",
    width: 180,
  },
  {
    label: "归因模式",
    prop: "ascribeModeRender",
    minWidth: 120,
  },
  {
    prop: "regionName",
    label: "环境",
    minWidth: 140,
  },
  {
    prop: "hasResultQueryCount",
    label: "有结果query数",
    minWidth: 150,
    custom: "link",
    blod: true,
    customRender: {
      click: (record: any) => {
        handleRirect(record, "hasResultQuery", "有结果query数")
      },
    },
  },
  {
    prop: "oneGoodDocQuery",
    label: "含≥1条goodUrl结果的query数",
    width: 250,
    custom: "link",
    blod: true,
    customRender: {
      click: (record: any) => {
        handleRirect(record, "oneGoodDoc", "含≥1条goodUrl结果的query数")
      },
    },
  },
  { prop: "hasResultAbsorbPercentageRender", label: "有结果吸收率", width: 160 },
  { prop: "docCount", label: "Doc数", width: 120 },

  { prop: "goodDocCount", label: "good doc", width: 120 },
  { prop: "goodDocPercentageRender", label: "good率", width: 120 },
  { prop: "badDocCount", label: "bad doc", width: 120 },
  { prop: "badDocPercentageRender", label: "bad率", width: 120 },
  {
    prop: "topKGoodQuery", label: "TopK完全good的query数", width: 230,
    custom: "link",
    blod: true,
    customRender: {
      click: (record: any) => {
        handleRirect(record, "topKGoodQuery", "TopK完全good的query数")
      },
    },
  },
  {
    prop: "topKGoodQueryPercentageRender",
    label: "TopK完全good率",
    width: 200,
  },
]);
const topK = ref<any>('');
const userGroup = ref<any>("");
const topKOptions = ref<any[]>([]);
const tableData = ref<any>([]);
const userGroupOptions = ref<any[]>([]);

const handleRirect = (item: any, column: string, title: string) => {
  let catalog = $router.currentRoute.value.query.catalog;
  $router.push({
    name: `evaluation-task::details::tag-details`,
    query: {
      metaLabel: [routeQuery.name, item.markUser + title],
      missionId: routeQuery.missionId,
      name: routeQuery.name,
      description: routeQuery.description,
      label: routeQuery.label,
      size: routeQuery.size,
      typeRender: routeQuery.typeRender,
      timeRender: routeQuery.timeRender,
      userCount: routeQuery.userCount,
      queryCount: routeQuery.queryCount,
      markUser: item.markUser,
      userType: item.userType,
      sceneProcessName: item.sceneProcessName,
      regionName: item.regionName,
      catalog: catalog,
      markRecordIds: column == "oneGoodDoc" ? item.oneGoodDocQueryRecordIds : column == "topKGoodQuery" ? item.topKGoodQueryRecordIds : [],
      column
    },
  });
};

//列表查询
const loadListData = (data: any) => {
  const params = {
    ...data,
    userGroup: userGroup.value
  };
  if (topK.value) {
    params.topK = topK.value
  } else {
    return Promise.resolve({})
  }
  return new Promise((resolve: any) => {
    missionApi
      .getAnalysisList({ ...params, missionId: routeQuery.missionId })
      .then((result) => {
        const arr = util.generateTableColumns(result.data);
        columns.value = defaultColumns.value.concat([...arr]);
        result.data = result.data.map((item) => ({
          ...item,
          ...item.extendMap,
        }));

        tableData.value = result.data;
        resolve(result);
      });
  });
};

const transformListData = (data: any) => {
  return data.map((x: any) => {
    x.markMinTimeRender = !dataC.isEmpty(x.markMinTime)
      ? timeC.format(x.markMinTime, "YYYY-MM-DD hh:mm:ss")
      : "";
    x.markMaxTimeRender = !dataC.isEmpty(x.markMaxTime)
      ? timeC.format(x.markMaxTime, "YYYY-MM-DD hh:mm:ss")
      : "";
    x.hasResultAbsorbPercentageRender = !dataC.isEmpty(x.hasResultAbsorbPercentage)
      ? x.hasResultAbsorbPercentage + '%'
      : "";
    x.goodDocPercentageRender = !dataC.isEmpty(x.goodDocPercentage)
      ? x.goodDocPercentage + '%'
      : "";
    x.badDocPercentageRender = !dataC.isEmpty(x.badDocPercentage)
      ? x.badDocPercentage + '%'
      : "";
    x.topKGoodQueryPercentageRender = !dataC.isEmpty(x.topKGoodQueryPercentage)
      ? x.topKGoodQueryPercentage + '%'
      : "";
    x.completePercentageRender = !dataC.isEmpty(x.completePercentage)
      ? x.completePercentage + '%'
      : "";
    x.ascribeModeRender = dataC.isEmpty(x.ascribeMode) ? "-" : x.ascribeMode == 0 ? "竞品good" : "自研bad";
    return x;
  });
};
const createSequence = (max) =>
  Array.from({ length: max }, (_, index) => index + 1).reverse();
const getTableData = () => {
  missionApi.getFilterparams(routeQuery.missionId).then((res) => {
    topKOptions.value = createSequence(res.data?.topK || 5).map(
      (item: any) => ({
        label: "top" + item,
        value: item,
      })
    );
    topK.value = createSequence(res.data?.topK || 5)[0];


    console.log("res.data?.groupNumber", res.data?.groupNumber);
    let groupNumber: number = res.data?.groupNumber || 0;
    // 生成分组选项
    userGroupOptions.value = Array.from({ length: groupNumber }, (_, i) => ({
      label: `分组${i + 1}`,
      value: `分组${i + 1}`,
    }));
    loadList()
  });
};

getTableData()
//初始化
onMounted(async () => {
});
//事件列表
const events = reactive({
  handleTopKChange: (val: any) => {
    topK.value = val
    loadList()
  },
  handleUserGroupChange: (val: any) => {
    userGroup.value = val
    loadList()
  },
  collapseChange: (val: string[]) => {
    window.dispatchEvent(new Event("resize"));
  },
  exportExcel: () => {
    missionApi
      .exportTopK(routeQuery.markCategoryId, { topK: topK.value, missionId: routeQuery.taskId })
      .then((res) =>
        util.downloadFile(
          res,
          `${routeQuery.metaLabel}任务详情.xlsx`
        )
      );
  },
});
const loadList = () => {
  proxy.$refs["myTableRef"]?.loadData();
};
//接口暴露
defineExpose({
  loadList,
  loadListData,
});
</script>
<style lang="scss">
.evaluation-task-container {
  padding: 10px;

  .info-card {
    .bold {
      font-weight: bold;
    }

    .collapse {
      border: none;

      .el-collapse-item__header {
        border: none;
        height: 23px;
        margin-bottom: 12px;
      }

      .el-collapse-item__wrap {
        border: none;
      }
    }

    .el-card__body {
      height: 100%;

      .el-collapse {
        height: 100%;

        .el-collapse-item {
          height: 100%;

          .el-collapse-item__wrap {
            height: 100%;

            .el-collapse-item__content {
              height: 100%;

              .el-descriptions {
                height: 100%;

                .el-descriptions__body {
                  height: 100%;
                  overflow-y: auto;
                }
              }
            }
          }
        }
      }
    }
  }

  .table-card {
    margin-top: 10px;

    .el-card__body {
      height: 100%;
    }

    .no-bottom {
      margin-bottom: 0;
    }
  }
}

.idx-db-statistic-site-table {
  height: calc(100% - 40px);

  .query-wrapper {
    padding: 0 !important;
  }

  .table-wrapper {
    padding: 0 !important;
  }
}
</style>
<style lang="scss" scoped>
::v-deep(.el-radio-group) {
  margin-bottom: 14px;
}

.total-info,
.inline-block {
  display: inline-block;
}

.name+.name {
  margin-left: 15px;
}
</style>