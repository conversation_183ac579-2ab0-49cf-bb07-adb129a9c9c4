<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DocAnnotation 样式测试</title>
    <style>
        .doc-header {
            display: flex;
            gap: 8px;
            border: 1px solid #e4e7ed;
            border-radius: 4px;
            overflow: hidden;
            margin: 20px;
            max-width: 800px;
        }

        .doc-field {
            display: flex;
            flex-direction: column;
            border-right: 1px solid #e4e7ed;
        }

        .doc-field:last-child {
            border-right: none;
        }

        .field-content {
            padding: 0 8px;
            display: flex;
            align-items: center;
            gap: 4px;
            min-height: 32px;
            font-size: 13px;
        }

        .sequence-field {
            flex: 0 0 30px;
        }

        .doc-index {
            background: #409eff;
            color: white;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
            text-align: center;
            padding: 4px;
        }

        .title-field {
            flex: 1;
            min-width: 0;
        }

        .title-field .field-content {
            overflow: hidden;
        }

        .title-link {
            max-width: 100%;
            text-align: left;
            justify-content: flex-start;
            overflow: hidden;
            background: none;
            border: none;
            color: #409eff;
            cursor: pointer;
            padding: 0;
            text-decoration: none;
        }

        .title-text {
            display: block;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            max-width: 100%;
        }

        .url-field {
            flex: 2;
            min-width: 0;
        }

        .url-field .field-content {
            overflow: hidden;
            position: relative;
        }

        .url-link {
            max-width: calc(100% - 24px);
            text-align: left;
            justify-content: flex-start;
            overflow: hidden;
            flex: 1;
            background: none;
            border: none;
            color: #409eff;
            cursor: pointer;
            padding: 0;
            text-decoration: none;
        }

        .url-text {
            display: block;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            max-width: 100%;
        }

        .icon-copy {
            cursor: pointer;
            color: #409eff;
            flex-shrink: 0;
            margin-left: 4px;
            width: 16px;
            height: 16px;
        }

        .icon-copy:hover {
            color: #66b1ff;
        }

        .tooltip {
            position: relative;
            display: inline-block;
        }

        .tooltip .tooltiptext {
            visibility: hidden;
            width: 300px;
            background-color: #303133;
            color: #fff;
            text-align: left;
            border-radius: 4px;
            padding: 8px;
            position: absolute;
            z-index: 1;
            bottom: 125%;
            left: 50%;
            margin-left: -150px;
            opacity: 0;
            transition: opacity 0.3s;
            font-size: 12px;
            word-break: break-all;
        }

        .tooltip:hover .tooltiptext {
            visibility: visible;
            opacity: 1;
        }
    </style>
</head>
<body>
    <h1>DocAnnotation 标题和URL样式测试</h1>
    
    <h2>测试1: 正常长度的标题和URL</h2>
    <div class="doc-header">
        <div class="doc-field sequence-field">
            <div class="field-content doc-index">1</div>
        </div>
        <div class="doc-field title-field">
            <div class="field-content">
                <div class="tooltip">
                    <button class="title-link">
                        <span class="title-text">正常长度的标题</span>
                    </button>
                    <span class="tooltiptext">正常长度的标题</span>
                </div>
            </div>
        </div>
        <div class="doc-field url-field">
            <div class="field-content">
                <div class="tooltip">
                    <button class="url-link">
                        <span class="url-text">https://example.com/normal-url</span>
                    </button>
                    <span class="tooltiptext">https://example.com/normal-url</span>
                </div>
                <span class="icon-copy">📋</span>
            </div>
        </div>
    </div>

    <h2>测试2: 超长标题和URL</h2>
    <div class="doc-header">
        <div class="doc-field sequence-field">
            <div class="field-content doc-index">2</div>
        </div>
        <div class="doc-field title-field">
            <div class="field-content">
                <div class="tooltip">
                    <button class="title-link">
                        <span class="title-text">这是一个非常非常非常非常非常非常非常非常非常非常非常非常非常非常非常非常长的标题，应该会被省略号截断</span>
                    </button>
                    <span class="tooltiptext">这是一个非常非常非常非常非常非常非常非常非常非常非常非常非常非常非常非常长的标题，应该会被省略号截断</span>
                </div>
            </div>
        </div>
        <div class="doc-field url-field">
            <div class="field-content">
                <div class="tooltip">
                    <button class="url-link">
                        <span class="url-text">https://example.com/very/very/very/very/very/very/very/very/very/very/very/very/very/very/very/very/long/url/path/that/should/be/truncated/with/ellipsis</span>
                    </button>
                    <span class="tooltiptext">https://example.com/very/very/very/very/very/very/very/very/very/very/very/very/very/very/very/very/long/url/path/that/should/be/truncated/with/ellipsis</span>
                </div>
                <span class="icon-copy">📋</span>
            </div>
        </div>
    </div>

    <h2>测试3: 极端情况 - 非常窄的容器</h2>
    <div class="doc-header" style="max-width: 400px;">
        <div class="doc-field sequence-field">
            <div class="field-content doc-index">3</div>
        </div>
        <div class="doc-field title-field">
            <div class="field-content">
                <div class="tooltip">
                    <button class="title-link">
                        <span class="title-text">窄容器中的长标题测试</span>
                    </button>
                    <span class="tooltiptext">窄容器中的长标题测试</span>
                </div>
            </div>
        </div>
        <div class="doc-field url-field">
            <div class="field-content">
                <div class="tooltip">
                    <button class="url-link">
                        <span class="url-text">https://example.com/narrow/container/test</span>
                    </button>
                    <span class="tooltiptext">https://example.com/narrow/container/test</span>
                </div>
                <span class="icon-copy">📋</span>
            </div>
        </div>
    </div>

    <script>
        // 模拟复制功能
        document.querySelectorAll('.icon-copy').forEach(icon => {
            icon.addEventListener('click', function() {
                const urlText = this.previousElementSibling.querySelector('.url-text').textContent;
                navigator.clipboard.writeText(urlText).then(() => {
                    alert('URL已复制到剪贴板: ' + urlText);
                });
            });
        });
    </script>
</body>
</html>
